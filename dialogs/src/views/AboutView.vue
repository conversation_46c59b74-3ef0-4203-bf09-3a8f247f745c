<template>
  <div class="about">
    <button class="coupon-btn" @click="showCouponModal = true">查看优惠券</button>

    <!-- 优惠券弹窗 -->
    <div v-if="showCouponModal" class="modal-overlay" @click="showCouponModal = false">
      <div class="coupon-modal" @click.stop>
        <!-- 弹窗头部：顶部背景和优惠券数量 -->
        <div class="modal-header">
          <img src="@/assets/bg/top_bg.png" alt="顶部背景" class="top-bg">
          <div class="coupon-count">您有 {{ coupons.length }} 张优惠券</div>
        </div>

        <!-- 悬浮的标题文本 -->
        <img src="@/assets/bg/top_title.png" alt="标题文本" class="top-title" :style="coupons.length > 2 ? 'top:26%' : ''"
          :class="{ 'single-coupon': coupons.length === 1 }">

        <!-- 中间内容区域：底部背景和优惠券列表 -->
        <div class="modal-content"
          :style="coupons.length === 1 ? 'padding: 30px 20px 0 20px;' : coupons.length === 2 ? 'padding: 25px 20px 0 20px;' : ''">
          <!-- 根据数据条数动态显示背景图 -->
          <img src="@/assets/bg/bottom_bg1.png" alt="底部背景" class="bottom-bg" v-if="coupons.length === 1">
          <img src="@/assets/bg/bottom_bg.png" alt="底部背景" class="bottom-bg" v-if="coupons.length > 1">
          <img src="@/assets/bg/lps.png" alt="左侧装饰" class="left-decoration">

          <!-- 优惠券列表容器，支持滑动 -->
          <div class="coupon-list-wrapper" :style="coupons.length === 2 ? 'margin-top: 10px;' : ''" :class="{
            'single-item': coupons.length === 1,
            'scrollable': coupons.length > 2,
            'expanded': coupons.length > 2 && showBottomInfo
          }">
            <div class="coupon-list-container" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd">
              <div class="coupon-list" :style="{ transform: `translateY(${scrollPosition}px)` }">
                <div v-for="(coupon, index) in coupons" :key="index" class="coupon-item"
                  :class="{ 'fade-item': shouldFadeItem(index) }"
                  :style="coupons.length > 2 ? 'margin-bottom: 10px;' : ''">
                  <img src="@/assets/bg/itembg.png" alt="优惠券背景" class="item-bg">
                  <div class="coupon-content">
                    <div class="coupon-amount"><span>¥</span>{{ coupon.amount }}</div>
                    <div class="coupon-info">
                      <div class="coupon-title">{{ coupon.title }}</div>
                      <div class="coupon-expire">有效期至{{ coupon.expireDate }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 渐变遮罩层 - 显示底部信息时隐藏 -->
            <div class="fade-mask" v-if="coupons.length > 2 && !showBottomInfo"></div>
          </div>

          <!-- 底部提示 - 基于showBottomInfo状态显示 -->
          <div class="more-info-container" v-if="coupons.length > 2 && showBottomInfo">
            <p class="more-info">更多卡券信息请至 我的-红包优惠券 中查看 &gt;</p>
          </div>
          <p v-if="coupons.length == 2" style="height: 10px;"></p>
        </div>

        <!-- 悬浮的去使用按钮 -->
        <div class="floating-btn-container" v-if="coupons.length > 2">
          <button class="use-btn floating" @click="useCoupon">去使用</button>
        </div>
        <!-- 非悬浮的去使用按钮（用于1-2条数据） -->
        <div class="normal-btn-container" v-else>
          <button class="use-btn" @click="useCoupon">去使用</button>
        </div>
        <!-- 关闭按钮 -->
        <button class="close-btn" @click="showCouponModal = false">
          <span class="close-icon">×</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AboutView',
  data () {
    return {
      showCouponModal: false,
      scrollPosition: 0,
      startY: 0,
      currentY: 0,
      showBottomInfo: false, // 新增：控制底部信息显示
      coupons: [
        {
          id: 1,
          amount: 20,
          title: '国内机票代金券',
          expireDate: '2024.02.16 23:59'
        },
        {
          id: 2,
          amount: 20,
          title: '国内机票代金券',
          expireDate: '2024.02.16 23:59'
        },
        {
          id: 3,
          amount: 20,
          title: '国内机票代金券',
          expireDate: '2024.02.16 23:59'
        },
        {
          id: 4,
          amount: 20,
          title: '国内机票代金券',
          expireDate: '2024.02.16 23:59'
        },
        {
          id: 5,
          amount: 20,
          title: '国内机票代金券',
          expireDate: '2024.02.16 23:59'
        }
      ]
    }
  },
  computed: {
    // 计算是否真正滑动到底部（用于精确判断）
    isReallyAtBottom () {
      if (this.coupons.length <= 2) return false

      // 动态获取实际的优惠券项目高度
      const itemHeight = this.getCouponItemHeight()

      // 计算最大滚动距离
      const maxScroll = Math.max(0, (this.coupons.length - 2.5) * itemHeight)

      // 更严格的判断：必须真正滑动到最底部
      return Math.abs(this.scrollPosition) >= maxScroll - 5 // 只留5px的容差
    }
  },
  mounted () {
    // 清除高度缓存
    this._cachedItemHeight = null

    // 确保DOM完全渲染后再进行高度计算
    this.$nextTick(() => {
      // 触发一次计算，确保计算能正确工作
      this.$forceUpdate()

      // 模拟数据加载完成，自动显示底部信息
      this.onDataLoaded()
    })
  },
  methods: {
    // 获取优惠券项目的实际高度
    getCouponItemHeight () {
      // 如果已经缓存了高度，直接返回
      if (this._cachedItemHeight) return this._cachedItemHeight

      const couponItems = this.$el?.querySelectorAll('.coupon-item')
      if (!couponItems || couponItems.length === 0) return 125 // 默认高度

      try {
        const firstItem = couponItems[0]
        const itemStyle = window.getComputedStyle(firstItem)
        const height = firstItem.offsetHeight +
          parseInt(itemStyle.marginTop || 0) +
          parseInt(itemStyle.marginBottom || 0) + 4

        // 缓存计算结果
        this._cachedItemHeight = height
        return height
      } catch (error) {
        console.warn('获取优惠券项目高度失败，使用默认值:', error)
        return 125 // 默认高度
      }
    },

    useCoupon () {
      this.showCouponModal = false
      alert('正在跳转到使用优惠券页面...')
    },

    // 判断是否需要淡化显示的项目
    shouldFadeItem (index) {
      if (this.coupons.length <= 2) return false

      // 简化逻辑：默认显示2.5条数据
      // 第3条（index=2）开始淡化，第4条及以后完全透明
      const visibleItems = 2.5
      const currentVisibleIndex = index + this.scrollPosition / 100

      // 如果当前项目超出了可见范围的2.5条，则需要淡化
      return currentVisibleIndex >= visibleItems - 0.5
    },

    // 触摸开始事件
    touchStart (e) {
      this.startY = e.touches[0].clientY
      this.currentY = this.scrollPosition
    },

    // 触摸移动事件
    touchMove (e) {
      const moveY = e.touches[0].clientY
      let newPosition = this.currentY + (moveY - this.startY)
      // 限制滑动范围 - 只在数据量超过2条时启用滚动限制
      if (this.coupons.length > 2) {
        // 使用动态获取的项目高度
        const itemHeight = this.getCouponItemHeight()
        const maxScroll = Math.max(0, (this.coupons.length - 2.3) * itemHeight)
        newPosition = Math.min(0, Math.max(-maxScroll, newPosition))
      } else {
        newPosition = 0
      }
      this.scrollPosition = newPosition
    },

    // 触摸结束事件 - 检查是否滑动到底部
    touchEnd () {
      // 只有在真正滑动到底部时才显示底部信息
      if (this.isReallyAtBottom) {
        this.showBottomInfo = true
      }
    },

    // 模拟数据加载完成后显示底部信息的方法
    // 你可以在实际的数据加载完成后调用这个方法
    onDataLoaded () {
      // 如果数据超过2条，延迟一点时间后显示底部信息
      if (this.coupons.length > 2) {
        setTimeout(() => {
          this.showBottomInfo = true
        }, 500) // 延迟500ms显示，给用户一个渐进的体验
      }
    }
  }
}
</script>

<style scoped>
.about {
  padding: 20px 15px;
  text-align: center;
}

.coupon-btn {
  padding: 10px 20px;
  background-color: #ff6b6b;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 15px;
  font-weight: bold;
}

.modal-overlay {
  position: fixed;
  top: -100px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.coupon-modal {
  width: 85%;
  max-width: 350px;
  position: relative;
  /* 移除overflow: hidden，让装饰元素可以超出边界 */
}

/* 弹窗头部样式 */
.modal-header {
  position: relative;
  top: 70px;
  left: 6px;
  z-index: 1;
}

.top-bg {
  width: 100%;
  height: auto;
}

.coupon-count {
  position: absolute;
  top: 26%;
  left: 6%;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 600;
  color: #FB6E09;
}

/* 悬浮标题文本 */
.top-title {
  position: absolute;
  top: 31%;
  left: 5%;
  width: 80%;
  height: auto;
  z-index: 10;
}

/* 只有一条数据时，调整标题位置 */
.top-title.single-coupon {
  top: 41%;
}


/* 中间内容区域样式 */
.modal-content {
  position: relative;
  padding: 20px 20px 80px 20px;
  /* 增加底部内边距，为悬浮按钮留出空间 */
  z-index: 2;
  border-radius: 20px;
  /* 将圆角应用到内容区域 */
  /* overflow: hidden; */
  /* 只对内容区域应用溢出隐藏 */
}

.bottom-bg {
  width: 100%;
  height: auto;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  border-radius: 20px;
  /* 应用完整圆角，与内容区域匹配 */
}

.left-decoration {
  position: absolute;
  top: 4px;
  left: -30px;
  width: 60px;
  height: auto;
  z-index: 5;
}

/* 优惠券列表外层包装器 */
.coupon-list-wrapper {
  position: relative;
}

/* 只有一条数据时，不设置固定高度 */
.coupon-list-wrapper.single-item {
  height: auto;
}

/* 大于2条数据时，设置固定高度并支持滚动 */
.coupon-list-wrapper.scrollable {
  height: 280px;
  /* 显示2.5个项目的高度 */
  overflow: hidden;
}

/* 优惠券列表容器 */
.coupon-list-container {
  height: 100%;
  overflow: hidden;
  touch-action: pan-y;
  /* 添加滚动条样式但不显示 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
  transition: height 0.3s ease;
}

/* 滑动到底部时，给coupon-list-container增加高度 */
.coupon-list-wrapper.scrollable.expanded .coupon-list-container {
  height: calc(100% + 70px);
  /* 增加70px高度，为文本和按钮腾出空间 */
}

/* 隐藏滚动条但保持滚动功能 */
.coupon-list-container::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari and Opera */
}

/* 渐变遮罩层 */
.fade-mask {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.3) 30%, rgba(255, 255, 255, 0.7) 60%, rgba(255, 255, 255, 0.95) 100%);
  pointer-events: none;
  border-radius: 10px;
  z-index: 10;
}

/* 优惠券列表 - 支持滑动 */
.coupon-list {
  transition: transform 0.3s ease;
}

.coupon-item {
  position: relative;
  margin-bottom: 15px;
  padding: 0;
  transition: opacity 0.3s ease;
}

/* 淡化显示的优惠券项目 */
.coupon-item.fade-item {
  opacity: 1;
}

.item-bg {
  width: 100%;
  height: auto;
  position: relative;
  z-index: 1;
}

.coupon-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 15px 16px;
  box-sizing: border-box;
  z-index: 2;
}

.coupon-amount {
  font-size: 34px;
  color: #FF6A39;
  font-weight: 600;
  margin-right: 15px;
}

.coupon-amount span {
  font-size: 18px;
}

.coupon-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 20px;
}

.coupon-title {
  font-size: 15px;
  color: #333;
  margin-bottom: 5px;
  font-weight: 500;
}

.coupon-expire {
  font-size: 12px;
  color: #999;
}

/* 悬浮按钮容器 */
.floating-btn-container {
  position: absolute;
  /* 改为absolute定位，相对于弹窗定位 */
  bottom: 70px;
  /* 增加底部距离，确保在弹窗内部 */
  left: 50%;
  transform: translateX(-50%);
  z-index: 30;
  width: 68%;
}

/* 普通按钮容器 */
.normal-btn-container {
  position: relative;
  z-index: 10;
}

/* 去使用按钮 */
.use-btn {
  width: 68%;
  padding: 15px;
  color: white;
  font-size: 18px;
  background-image: url('@/assets/bg/bbg.png');
  background-size: cover;
  background-position: center;
  font-weight: bold;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto;
  border: none;
  border-radius: 25px;
}

/* 悬浮状态的按钮 */
.use-btn.floating {
  width: 100%;
  /* 添加阴影效果 */
  margin: 0;
  /* 移除默认边距 */
}

/* 底部提示文本容器 */
.more-info-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 136px;
}

/* 底部提示文本 */
.more-info {
  font-size: 12px;
  color: #333;
  margin: 0;
  text-align: center;
}

/* 关闭按钮 - 移到弹窗底部 */
.close-btn {
  position: absolute;
  bottom: -80px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ff6b6b;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
  padding: 0;
  box-shadow: 0 2px 10px rgba(255, 107, 107, 0.5);
  /* 恢复阴影效果 */
}

.close-icon {
  color: white;
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}
</style>
